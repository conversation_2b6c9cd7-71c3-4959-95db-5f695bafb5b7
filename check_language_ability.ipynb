{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 检查语言output能力"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.10/dist-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n", "/usr/local/lib/python3.10/dist-packages/transformers/utils/hub.py:105: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-07-08 12:05:57,620] [INFO] [real_accelerator.py:203:get_accelerator] Setting ds_accelerator to cuda (auto detect)\n", "\u001b[93m [WARNING] \u001b[0m Please specify the CUTLASS repo directory as environment variable $CUTLASS_PATH\n", "\u001b[93m [WARNING] \u001b[0m sparse_attn requires a torch version >= 1.5 and < 2.0 but detected 2.5\n", "\u001b[93m [WARNING] \u001b[0m using untested triton version (3.1.0), only 1.0.0 is known to be compatible\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.10/dist-packages/deepspeed/runtime/zero/linear.py:49: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.\n", "  def forward(ctx, input, weight, bias=None):\n", "/usr/local/lib/python3.10/dist-packages/deepspeed/runtime/zero/linear.py:67: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.\n", "  def backward(ctx, grad_output):\n", "You are attempting to use Flash Attention 2.0 with a model not initialized on GPU. Make sure to move the model to GPU after initializing it on CPU with `model.to('cuda')`.\n", "Loading checkpoint shards: 100%|██████████| 4/4 [00:15<00:00,  3.92s/it]\n", "Using a slow image processor as `use_fast` is unset and a slow processor was saved with this model. `use_fast=True` will be the default behavior in v4.52, even if the model was saved with a slow processor. This will result in minor differences in outputs. You'll still be able to use a slow processor with `use_fast=False`.\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import os\n", "import sys\n", "# NOTE enable this in single script\n", "# current_file_path = os.path.dirname(os.path.abspath(__file__))\n", "# module_path = os.path.join(current_file_path, \"../..\")\n", "# sys.path.append(module_path)\n", "from dataclasses import asdict\n", "import math\n", "from pathlib import Path\n", "from typing import List, Optional\n", "import yaml\n", "import debugpy\n", "\n", "from peft import LoraConfig, get_peft_model, prepare_model_for_kbit_training\n", "import torch\n", "import transformers\n", "from transformers import Trainer, is_datasets_available\n", "import datasets\n", "from transformers.integrations import deepspeed\n", "from torch.utils.data import Dataset, ConcatDataset, WeightedRandomSampler, RandomSampler, DataLoader\n", "\n", "from transformers import AutoProcessor, Qwen2_5_VLForConditionalGeneration\n", "from models.qwen2_5_vl import Qwen2_5_VLRetForConditionalGeneration\n", "\n", "from arguments import ModelArguments, DataArguments, TrainingArguments, LoraArguments\n", "from collators import COLLATORS\n", "from dataset.datasets_mbeir import LazySupervisedDataset, MbeirLanguageDataset\n", "from dataset.datasets_xhs import XHSDataset\n", "from dataset.datasets_dam import DAMDataset\n", "# from dataset.datasets_mmeb import MMEBDataset\n", "from loaders import LOADERS\n", "from supported_models import MODULE_KEYWORDS\n", "from utils import (\n", "    rank0_print\n", ")\n", "    \n", "# model = Qwen2_5_VLForConditionalGeneration.from_pretrained(\"./checkpoints/Qwen2.5-VL-7B-Dam\", attn_implementation= \"flash_attention_2\", torch_dtype=torch.bfloat16, low_cpu_mem_usage=True).cuda()\n", "# model = Qwen2_5_VLRetForConditionalGeneration.from_pretrained(\"./checkpoints/Qwen2.5-VL-7B-Dam\", attn_implementation= \"flash_attention_2\", torch_dtype=torch.bfloat16, low_cpu_mem_usage=True).cuda()\n", "model = Qwen2_5_VLRetForConditionalGeneration.from_pretrained(\"./checkpoints/qwen2_5-vl-7b_DAM_pretrain_vision\", attn_implementation= \"flash_attention_2\", torch_dtype=torch.bfloat16, low_cpu_mem_usage=True).cuda()\n", "processor = AutoProcessor.from_pretrained(\"Qwen/Qwen2.5-VL-7B-Instruct\")\n", "# processor = AutoProcessor.from_pretrained(\"./checkpoints/qwen2_5-vl-7b_DAM_pretrain_vision\")\n", "tokenizer = processor.tokenizer \n", "tokenizer.padding_side  = 'left'\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["\n", "device = 'cuda:0'\n", "\n", "def tensors_to_device(data, device, dtype=model.dtype):\n", "    for key in data.keys():\n", "        if isinstance(data[key], torch.Tensor):\n", "            if key == 'pixel_values':\n", "                data[key] = data[key].to(device).to(dtype)\n", "            else:\n", "                data[key] = data[key].to(device)\n", "    return data \n", "\n", "\n", "model.to(device)\n", "model.eval()\n", "model.config.emb_token_ids = [-777]"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["@decode inputs: ['system\\nYou are a helpful assistant.\\nuser\\n\\nDescribe the image in detail.\\nassistant\\nA rectangular window divided into four equal panes by two vertical and one horizontal wooden muntin. The window frame is dark brown, and the glass panes are clear with a slight reflection.']\n", "@decode inputs: ['system\\nYou are a helpful assistant.\\nuser\\n\\nDescribe the image in detail.\\nassistant\\nThe river has a smooth, light gray surface with subtle ripples running horizontally across it. The water appears calm and reflective, with a slight gradient from a lighter shade at the top to a slightly darker shade towards the bottom.']\n", "@decode inputs: ['system\\nYou are a helpful assistant.\\nuser\\n\\nDescribe the image in detail.\\nassistant\\nThe dress features a vibrant, multi-colored pattern with intricate designs and floral motifs. The bodice is adorned with a rich, golden fabric that contrasts beautifully with the colorful skirt. The skirt is layered, with a series of ruffled and pleated det']\n", "@decode inputs: ['system\\nYou are a helpful assistant.\\nuser\\n\\nDescribe the image in detail.\\nassistant\\nA book with a white cover featuring black text and a red logo at the top left corner. The title \"The Art of War\" is prominently displayed in large, bold font. Below the title, there is a subtitle in smaller font that reads \"A Classic of Military Strategy.\"']\n", "@decode inputs: ['system\\nYou are a helpful assistant.\\nuser\\n\\nDescribe the image in detail.\\nassistant\\nA cylindrical wooden post with a rough, weathered surface featuring vertical grain patterns and a few horizontal lines. The post has a slightly tapered top and bottom, with a visible knot hole near the top.']\n", "@decode inputs: ['system\\nYou are a helpful assistant.\\nuser\\n\\nDescribe the image in detail.\\nassistant\\nA cylindrical concrete pillar with a rough, textured surface. The pillar has two vertical metal bands wrapped around it, one near the top and another closer to the bottom. The metal bands have a slightly worn appearance.']\n", "@decode inputs: ['system\\nYou are a helpful assistant.\\nuser\\n\\nDescribe the image in detail.\\nassistant\\nA yellow tent with a green logo that reads \"SUNSET\" in bold, black letters. The tent has a smooth, slightly curved surface and a small, triangular section at the top right corner.']\n", "@decode inputs: ['system\\nYou are a helpful assistant.\\nuser\\n\\nDescribe the image in detail.\\nassistant\\nA rectangular table with a dark, smooth surface and a black frame. The table has four legs, each with a slight outward curve at the bottom. The frame is made of a single, continuous piece of material that forms the legs and the sides of the table.']\n", "@decode inputs: ['system\\nYou are a helpful assistant.\\nuser\\n\\nDescribe the image in detail.\\nassistant\\nThe train features a sleek, modern design with a predominantly white body and a bold red stripe running horizontally along its side. The windows are large and rectangular, with a black frame. The lower part of the train has a gray panel with a series of ']\n", "@decode inputs: ['system\\nYou are a helpful assistant.\\nuser\\n\\nDescribe the image in detail.\\nassistant\\nA modern building with a rectangular facade featuring a series of rectangular windows arranged in a grid pattern. The windows are divided into smaller panes, and some have dark frames. The building has a light-colored exterior with a flat roof. There is a s']\n"]}], "source": ["\n", "from dataset.datasets_dam import DAMDataset\n", "dam_dataset = DAMDataset(\n", "    data_path=\"/mnt/tidalfs-hssh01/dataset/mmeb/describe-anything-data\",\n", "    max_length=10000,\n", "    mode='crop', inference=True\n", ")\n", "\n", "data_collator = COLLATORS['qwen2_5-vl-7b'](\n", "    tokenizer=tokenizer,\n", "    processor=processor,\n", ")\n", "\n", "dataloader = DataLoader(dam_dataset, batch_size=1, num_workers=2, shuffle=False, collate_fn=data_collator)\n", "dataloader = iter(dataloader)\n", "\n", "\n", "# TODO : 跑之前要把dam ds返回的改成1\n", "datalist = [d for d,i in zip(dataloader, range(10))]\n", "\n", "with torch.no_grad():\n", "    for i, data in enumerate(datalist):\n", "        # data = next(dataloader)\n", "        batch = tensors_to_device(data, device)\n", "        output = model.generate(**batch, max_new_tokens=512)\n", "        image_generated_ids = output\n", "        image_input_length = batch['input_ids'].shape[1]\n", "        image_generated_only_ids = image_generated_ids[:, image_input_length:]\n", "\n", "        # 解码生成的 token IDs 为文本\n", "        # `skip_special_tokens=True` 会移除像 <|endoftext|> 这样的特殊标记\n", "        image_decoded_outputs = tokenizer.batch_decode(image_generated_ids, skip_special_tokens=True)\n", "        print(\"@decode inputs:\" ,image_decoded_outputs)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/plain": ["Parameter containing:\n", "tensor([2.0625, 2.1875, 2.2344,  ..., 2.1875, 2.1875, 2.2188], device='cuda:0',\n", "       dtype=torch.bfloat16, requires_grad=True)"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["i=-1\n", "# model.visual.context_layers[i].mlp_factor\n", "model.visual.context_layers[i].mlp_factor\n", "model.visual.context_layers[i].norm1.weight"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import pycocotools\n", "from PIL import Image\n", "import pickle\n", "\n", "def counts_to_mask(maskrle):\n", "    return np.array(pycocotools.mask.decode(maskrle), dtype=np.float32)\n", "\n", "def visualize_mask_on_image_pil(original_pil, binary_mask_np, \n", "                                color=(255, 0, 0), alpha_percent=50):\n", "    height, width = binary_mask_np.shape\n", "    if original_pil.size != (width, height):\n", "        print(f\"Warning: Mask size ({width},{height}) and image size ({original_pil.size}) differ.\")\n", "    alpha_value = int((alpha_percent / 100.0) * 255)\n", "    colored_mask_pil = Image.new(\"RGBA\", original_pil.size, (0, 0, 0, 0))\n", "    mask_rgba_np = np.zeros((height, width, 4), dtype=np.uint8)\n", "    \n", "    mask_indices = binary_mask_np == 1\n", "    mask_rgba_np[mask_indices] = list(color) + [alpha_value]\n", "\n", "    colored_mask_pil_from_np = Image.fromarray(mask_rgba_np, \"RGBA\")\n", "    original_pil.putalpha(255)\n", "    overlaid_image_pil = Image.alpha_composite(original_pil, colored_mask_pil_from_np)    \n", "    return overlaid_image_pil\n", "\n", "def mask2box(mask):\n", "    box = None\n", "    pos = np.where(mask > 0)\n", "    height, width = mask.shape\n", "\n", "    if pos[0].size > 0 and pos[1].size > 0:\n", "        x_min = np.min(pos[1]) / width\n", "        x_max = np.max(pos[1]) / width\n", "        y_min = np.min(pos[0]) / height\n", "        y_max = np.max(pos[0]) / height\n", "        box = [x_min, y_min, x_max, y_max]\n", "    return box\n", "\n", "\n", "dataset = dam_dataset.dataset['SAM']['train']\n", "idx = (12,0)\n", "anno = pickle.loads(dataset[idx[0]]['pickle'])\n", "print(anno[idx[1]])\n", "mask = counts_to_mask(anno[idx[1]]['mask_rle'])\n", "box = mask2box(mask)\n", "# print(anno[idx[1]]['category'])\n", "# print(anno[idx[1]]['caption'])\n", "img = dataset[idx[0]]['jpg']\n", "print(box)\n", "width, height = img.size\n", "x0 = width * box[0]\n", "y0 = height * box[1]\n", "x1 = width * box[2]\n", "y1 = height * box[3]\n", "\n", "img.crop((x0, y0, x1, y1)).show()\n", "visualize_mask_on_image_pil(img, mask)"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["(1500, 2060)"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset[idx[0]]['jpg'].size"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 测试xhs的描述"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from dataset.datasets_xhs import XHSDataset\n", "from dataset.datasets_mbeir import LazySupervisedDataset, MbeirLanguageDataset\n", "mnt = \"tidalfs-hssh01\"\n", "xhs_dataset = XHSDataset(\n", "    query_data_path=f\"/mnt/{mnt}/dataset/mmeb/M-BEIR/query/train/mbeir_xhsnote_task7_train.jsonl\",\n", "    cand_pool_path=f\"/mnt/{mnt}/dataset/mmeb/M-BEIR/cand_pool/local/mbeir_xhsnote_task7_cand_pool.jsonl\",\n", "    instructions_path=f\"/mnt/{mnt}/dataset/mmeb/M-BEIR/instructions/query_instructions.tsv\",\n", "    image_path_prefix=f\"/mnt/{mnt}/dataset/M-BEIR\",\n", "    tokenizer=tokenizer \n", ")\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[([{'role': 'user', 'content': [{'type': 'image', 'image': '/mnt/tidalfs-hssh01/dataset/mmeb/xhs_data/note_data/20250304/images/1040g0083189pmk643k0g5n7rfq898st4v5d9jmg.jpg', 'box': [0.35006, 0.76561, 0.59137, 0.96301]}, {'type': 'text', 'text': '\\nDescribe the image in detail.'}]}], [{'role': 'user', 'content': [{'type': 'image', 'image': '/mnt/tidalfs-hssh01/dataset/mmeb/xhs_data/note_data/20250304/images/1040g2sg313udgnsoh87g5oppbr9m5fp8hrmo6n8.jpg', 'box': [0.46301, 0.3773, 0.92572, 0.75576]}, {'type': 'text', 'text': '\\nDescribe the image in detail.'}]}])]\n", "[([{'role': 'user', 'content': [{'type': 'image', 'image': '/mnt/tidalfs-hssh01/dataset/mmeb/xhs_data/note_data/20250304/images/active_search_1040g0mg3181i0slljk1g49uktbgs606gp305c58.jpg', 'box': [0.3, 0.31, 0.9, 0.56]}, {'type': 'text', 'text': '\\nDescribe the image in detail.'}]}], [{'role': 'user', 'content': [{'type': 'image', 'image': '/mnt/tidalfs-hssh01/dataset/mmeb/xhs_data/note_data/20250304/images/1040g2sg3133v933038105n41s1blj9dmalkc2uo.jpg', 'box': [0.19748, 0.39252, 0.88644, 0.80143]}, {'type': 'text', 'text': '\\nDescribe the image in detail.'}]}])]\n", "[([{'role': 'user', 'content': [{'type': 'image', 'image': '/mnt/tidalfs-hssh01/dataset/mmeb/xhs_data/note_data/20250304/images/active_search_1040g0mg315bp74ahgu105ndumvp08tmf6q06guo.jpg', 'box': [0.18, 0.36, 0.9, 0.67]}, {'type': 'text', 'text': '\\nDescribe the image in detail.'}]}], [{'role': 'user', 'content': [{'type': 'image', 'image': '/mnt/tidalfs-hssh01/dataset/mmeb/xhs_data/note_data/20250304/images/1040g00830nhlnkml7e004a0murrb9h3tqe1oimo.jpg', 'box': [0.01178, 0.28508, 0.98734, 0.96398]}, {'type': 'text', 'text': '\\nDescribe the image in detail.'}]}])]\n", "@decode inputs: ['system\\nYou are a helpful assistant.\\nuser\\n\\nDescribe the image in detail.\\nassistant\\nA black, long-sleeved shirt with a fitted silhouette and a high neckline. The fabric appears to be smooth and slightly shiny, with a subtle sheen that reflects light. The sleeves are long and extend past the elbows, ending in a clean hem. The shirt is worn ', 'system\\nYou are a helpful assistant.\\nuser\\n\\nDescribe the image in detail.\\nassistant\\nA black, long-sleeved shirt with a fitted silhouette and a crew neckline. The fabric appears to be smooth and slightly stretchy, with a subtle sheen. The sleeves are long and end at the wrists, which are not visible in the image. The shirt is worn over a blac']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["@decode inputs: ['system\\nYou are a helpful assistant.\\nuser\\n\\nDescribe the image in detail.\\nassistant\\nA black, long-sleeved dress with a fitted bodice and a flared skirt. The dress features a high neckline and a zipper closure on the left side. The fabric appears to be smooth and slightly shiny, with a subtle texture that suggests a satin-like material. Th', 'system\\nYou are a helpful assistant.\\nuser\\n\\nDescribe the image in detail.\\nassistant\\nA black dress with a fitted bodice and a flared skirt. The bodice features a deep V-neckline and a high waistline, accentuated by a belt with a decorative buckle. The skirt is adorned with a pattern of white and silver metallic flowers and leaves, creating']\n"]}], "source": ["\n", "data_collator = COLLATORS['qwen2_5-vl-7b'](\n", "    tokenizer=tokenizer,\n", "    processor=processor,\n", ")\n", "\n", "dataloader = DataLoader(xhs_dataset, batch_size=1, num_workers=0, shuffle=False, collate_fn=data_collator)\n", "dataloader = iter(dataloader)\n", "\n", "\n", "# TODO : 跑之前要把dataset_xhs的prompt改成描述类型的；吧mbeir_dataset中get random poscand改成固定的\n", "datalist = [d for d,i in zip(dataloader, range(2))]\n", "\n", "with torch.no_grad():\n", "    for i, data in enumerate(datalist):\n", "        # data = next(dataloader)\n", "        batch = tensors_to_device(data, device)\n", "        output = model.generate(**batch, max_new_tokens=1024)\n", "        image_generated_ids = output\n", "        image_input_length = batch['input_ids'].shape[1]\n", "        image_generated_only_ids = image_generated_ids[:, image_input_length:]\n", "\n", "        # 解码生成的 token IDs 为文本\n", "        # `skip_special_tokens=True` 会移除像 <|endoftext|> 这样的特殊标记\n", "        image_decoded_outputs = tokenizer.batch_decode(image_generated_ids, skip_special_tokens=True)\n", "        print(\"@decode inputs:\" ,image_decoded_outputs)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["手动打开图片并且渲染路径"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["xhs_dataset[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from PIL import Image\n", "\n", "Image.open(\n", "'/mnt/tidalfs-hssh01/dataset/mmeb/xhs_data/note_data/20250304/images/1040g2sg3133v933038105n41s1blj9dmalkc2uo.jpg'\n", ").show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 4}